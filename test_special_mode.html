<!DOCTYPE html>
<html>
<head>
    <title>特殊模式配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .config-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .config-item { margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007cba; }
    </style>
</head>
<body>
    <h1>特殊模式配置测试</h1>
    
    <div class="config-section">
        <h3>设置特殊模式配置</h3>
        <div class="config-item">
            <label>
                <input type="checkbox" id="enableSpecialMode"> 启用特殊模式
            </label>
        </div>
        <div class="config-item">
            <label>特殊模式支付方式:</label>
            <select id="specialPayType">
                <option value="AA">支付宝</option>
                <option value="WX">微信</option>
            </select>
        </div>
        <div class="config-item">
            <label>特殊模式余额使用:</label>
            <select id="specialUseBalance">
                <option value="">不使用</option>
                <option value="useBalance">使用余额</option>
            </select>
        </div>
        <div class="config-item">
            <label>
                <input type="checkbox" id="specialUseMainAccount" checked> 使用主账号
            </label>
        </div>
        <button onclick="saveSpecialConfig()">保存配置</button>
    </div>
    
    <div class="config-section">
        <h3>测试配置读取</h3>
        <button onclick="testConfigReading()">测试配置读取</button>
        <button onclick="testOrderSettings('test123')">测试普通订单配置</button>
        <button onclick="testOrderSettings('123456')">测试特殊模式配置</button>
        <button onclick="clearLogs()">清除日志</button>
    </div>
    
    <div id="logs"></div>

    <script>
        // 复制核心配置函数
        function isSpecialModeEnabledLocal() {
            return localStorage.getItem('enableSpecialMode') === 'true';
        }

        function getLocalOrderSettings(saleId) {
            const useSpecialMode = isSpecialModeEnabledLocal() && saleId && !saleId.toLowerCase().includes('k');
            
            const settings = {
                cdk: {
                    payType: localStorage.getItem(useSpecialMode ? 'specialCdkPayType' : 'cdkPayType') || 'AA',
                    useBalance: localStorage.getItem(useSpecialMode ? 'specialCdkUseBalance' : 'cdkUseBalance') || '',
                    useMainAccount: localStorage.getItem(useSpecialMode ? 'specialCdkUseMainAccount' : 'cdkUseMainAccount') !== 'false'
                },
                isSpecialMode: useSpecialMode
            };
            
            return settings;
        }

        function log(message) {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = 'log';
            logEntry.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logEntry);
        }

        function saveSpecialConfig() {
            const enableSpecialMode = document.getElementById('enableSpecialMode').checked;
            const specialPayType = document.getElementById('specialPayType').value;
            const specialUseBalance = document.getElementById('specialUseBalance').value;
            const specialUseMainAccount = document.getElementById('specialUseMainAccount').checked;

            localStorage.setItem('enableSpecialMode', enableSpecialMode);
            localStorage.setItem('specialCdkPayType', specialPayType);
            localStorage.setItem('specialCdkUseBalance', specialUseBalance);
            localStorage.setItem('specialCdkUseMainAccount', specialUseMainAccount);

            log(`配置已保存: 特殊模式=${enableSpecialMode}, 支付方式=${specialPayType}, 余额=${specialUseBalance}, 主账号=${specialUseMainAccount}`);
        }

        function testConfigReading() {
            const enableSpecialMode = localStorage.getItem('enableSpecialMode');
            const specialPayType = localStorage.getItem('specialCdkPayType');
            const specialUseBalance = localStorage.getItem('specialCdkUseBalance');
            const specialUseMainAccount = localStorage.getItem('specialCdkUseMainAccount');

            log(`读取配置: enableSpecialMode=${enableSpecialMode}, specialCdkPayType=${specialPayType}, specialCdkUseBalance=${specialUseBalance}, specialCdkUseMainAccount=${specialUseMainAccount}`);
        }

        function testOrderSettings(saleId) {
            const settings = getLocalOrderSettings(saleId);
            log(`SaleID: ${saleId} - 特殊模式: ${settings.isSpecialMode}, 支付方式: ${settings.cdk.payType}, 余额: ${settings.cdk.useBalance}, 主账号: ${settings.cdk.useMainAccount}`);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // 页面加载时读取当前配置
        window.onload = function() {
            document.getElementById('enableSpecialMode').checked = localStorage.getItem('enableSpecialMode') === 'true';
            document.getElementById('specialPayType').value = localStorage.getItem('specialCdkPayType') || 'AA';
            document.getElementById('specialUseBalance').value = localStorage.getItem('specialCdkUseBalance') || '';
            document.getElementById('specialUseMainAccount').checked = localStorage.getItem('specialCdkUseMainAccount') !== 'false';
        };
    </script>
</body>
</html>
