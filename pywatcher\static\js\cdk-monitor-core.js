/**
 * CDK监控和自动下单核心逻辑
 * 包含价格监控、目标价格检查和自动下单功能
 */

// 下单配置预热缓存
let preloadedOrderSettings = {
    normal: null,
    special: null,
    lastUpdate: 0
};

/**
 * 预热下单配置，避免下单时才读取
 */
function preloadOrderSettings() {
    const now = Date.now();
    // 每5分钟更新一次缓存
    if (now - preloadedOrderSettings.lastUpdate > 300000) {
        // 预热正常模式配置
        preloadedOrderSettings.normal = {
            useMainAccount: localStorage.getItem('cdkUseMainAccount') !== 'false',
            payType: localStorage.getItem('cdkPayType') || 'AA',
            useBalance: localStorage.getItem('cdkUseBalance') || '',
            isSpecialMode: false
        };

        // 预热特殊模式配置
        if (isSpecialModeEnabledLocal()) {
            preloadedOrderSettings.special = {
                useMainAccount: localStorage.getItem('specialCdkUseMainAccount') !== 'false',
                payType: localStorage.getItem('specialCdkPayType') || 'AA',
                useBalance: localStorage.getItem('specialCdkUseBalance') || '',
                isSpecialMode: true
            };
        }

        preloadedOrderSettings.lastUpdate = now;
    }
}

/**
 * 快速获取下单配置（使用预热缓存）
 */
function getFastOrderSettings(saleId) {
    preloadOrderSettings(); // 确保配置是最新的

    const useSpecialMode = isSpecialModeEnabledLocal() && saleId && !saleId.toLowerCase().includes('k');

    if (useSpecialMode && preloadedOrderSettings.special) {
        return {
            cdk: preloadedOrderSettings.special,
            isSpecialMode: true
        };
    } else {
        return {
            cdk: preloadedOrderSettings.normal,
            isSpecialMode: false
        };
    }
}

/**
 * 检查是否启用特殊模式（内置版本）
 */
function isSpecialModeEnabledLocal() {
    return localStorage.getItem('enableSpecialMode') === 'true';
}

/**
 * 获取下单配置函数（内置版本）
 * @param {string} saleId - 商品ID，用于判断是否使用特殊模式配置
 */
function getLocalOrderSettings(saleId) {
    const useSpecialMode = isSpecialModeEnabledLocal() && saleId && !saleId.toLowerCase().includes('k');
    
    const settings = {
        cdk: {
            payType: localStorage.getItem(useSpecialMode ? 'specialCdkPayType' : 'cdkPayType') || 'AA',
            useBalance: localStorage.getItem(useSpecialMode ? 'specialCdkUseBalance' : 'cdkUseBalance') || '',
            useMainAccount: localStorage.getItem(useSpecialMode ? 'specialCdkUseMainAccount' : 'cdkUseMainAccount') !== 'false'
        },
        chest: {
            payType: localStorage.getItem(useSpecialMode ? 'specialChestPayType' : 'chestPayType') || 'AA',
            useBalance: localStorage.getItem(useSpecialMode ? 'specialChestUseBalance' : 'chestUseBalance') || '',
            useMainAccount: localStorage.getItem(useSpecialMode ? 'specialChestUseMainAccount' : 'chestUseMainAccount') !== 'false'
        },
        isSpecialMode: useSpecialMode
    };
    
    return settings;
}

// 检查并处理目标价格命中 - 最高优先级处理
async function checkAndHandleTargetPrice(game, gameIndex, currentPrice, sellersData) {
    const targetPrice = monitoredGames[gameIndex].target_price;
    
    // 检查是否命中目标价格
    if (!targetPrice || currentPrice > targetPrice) {
        return false; // 未命中
    }
    
    // 命中目标价格 - 立即处理
    addCdkLog(`🎯 游戏 ${game.name} 达到目标价格！当前: ¥${currentPrice}, 目标: ¥${targetPrice}`, false, true);
    
    // 发送浏览器通知
    if (monitoredGames[gameIndex].price_notification_type === 'browser') {
        sendPriceNotification(monitoredGames[gameIndex], currentPrice);
    }
    
    // 自动下单处理 - 最高优先级
    if (localStorage.getItem('cdkAutoOrder') !== 'false') {
        const orderResult = await attemptAutoOrder(game, sellersData);
        return orderResult; // 返回是否成功下单
    }
    
    return true; // 命中但未启用自动下单
}

// 尝试自动下单
async function attemptAutoOrder(game, sellersData) {
    if (!sellersData || sellersData.length === 0 || !sellersData[0].saleId) {
        addCdkLog(`❌ 游戏 ${game.name} 自动下单失败：无有效的卖家ID`, true);
        return false;
    }
    
    // 检查是否正在处理支付或兑换过程中
    const gameProcessing = localStorage.getItem(`processing_${game.id}`);
    if (gameProcessing) {
        addCdkLog(`⚠️ 游戏 ${game.name} 正在处理支付/兑换过程中，跳过重复下单`, false);
        return false;
    }
    
    const saleId = sellersData[0].saleId;
    const price = sellersData[0].keyPrice;
    
    // 跳过带k且价格为0.1的可疑订单
    if (saleId.toLowerCase().includes('k') && price === 0.1) {
        addCdkLog(`⚠️ 跳过可疑订单 ${saleId}（带k且价格：¥${price}）`, false, true);
        return false;
    }
    
    const orderedSaleIds = JSON.parse(localStorage.getItem(`ordered_${game.id}`) || '[]');
    
    if (orderedSaleIds.includes(saleId)) {
        addCdkLog(`⚠️ 游戏 ${game.name} 已经尝试对卖家ID ${saleId} 下单，跳过重复下单`, false);
        return false;
    }
    
    try {
        addCdkLog(`🚀 游戏 ${game.name} 价格命中目标，立即执行自动下单！`, false, true);

        // 获取实际成交价格和卖家信息
        const actualPrice = sellersData[0].keyPrice || 0;
        const sellerSold = sellersData[0].sold || 0;
        const sellerName = sellersData[0].steamName || '未知';

        // 风险控制：跳过带k且交易数为0的卖家
        if (saleId.toLowerCase().includes('k') && sellerSold === 0) {
            addCdkLog(`⚠️ 跳过风险卖家：${sellerName} (ID: ${saleId}) - 带k且交易数为0`, false, true);
            return false;
        }

        // 记录已尝试下单的卖家ID
        orderedSaleIds.push(saleId);
        localStorage.setItem(`ordered_${game.id}`, JSON.stringify(orderedSaleIds));

        const sellerInfo = {
            saleId: saleId,
            actualPrice: actualPrice,
            sold: sellerSold,
            steamName: sellerName,
            gameId: game.id  // 性能优化：传递gameId避免后续查找
        };
        
        // 执行下单
        await placeCdkOrder(game.name, saleId, actualPrice, sellerInfo);
        
        addCdkLog(`✅ 游戏 ${game.name} 自动下单请求已发送`, false, true);
        
        // 下单请求已发送，等待placeCdkOrder函数处理后续逻辑
        // 移除监控的逻辑在placeCdkOrder中根据实际下单结果处理

        return true;

    } catch (error) {
        addCdkLog(`❌ 游戏 ${game.name} 自动下单失败: ${error.message}`, true);
        // 下单失败不移除监控列表
        return false;
    }
}

// 下单购买CDK
async function placeCdkOrder(gameName, saleId, actualPrice = 0, sellerInfo = null) {
    try {
        // 使用预热的快速配置获取，提升下单速度
        const orderSettings = (typeof getCurrentOrderSettings === 'function') ? getCurrentOrderSettings(saleId) : getFastOrderSettings(saleId);
        const isMainAccount = orderSettings.cdk.useMainAccount;
        const payType = orderSettings.cdk.payType;
        const walletFlag = orderSettings.cdk.useBalance;

        // 添加详细的配置日志
        addCdkLog(`📋 下单配置 - 特殊模式: ${orderSettings.isSpecialMode ? '是' : '否'}, 支付方式: ${payType}, 余额使用: ${walletFlag || '否'}, 主账号: ${isMainAccount ? '是' : '否'}`, false, true);


        
        // 性能优化：移除冗余日志，直接准备下单参数
        const params = new URLSearchParams({
            saleId: saleId,
            payType: payType,
            promoCodeId: '',
            walletFlag: walletFlag,
            version: 'v1',
            is_main_account: isMainAccount.toString(),
            orderType: 'cdk',
            gameName: gameName
        });

        // 如果有卖家信息，添加到参数中
        if (sellerInfo) {
            params.append('sellerSold', sellerInfo.sold.toString());
            params.append('sellerName', sellerInfo.steamName);
            addCdkLog(`📊 卖家信息传递 - 名称: ${sellerInfo.steamName}, 交易数: ${sellerInfo.sold}`, false, true);
        }

        // 性能优化：直接发起下单请求，减少日志输出
        addCdkLog(`🔗 请求URL: /api/xboot/steamKeyOrder/payOrder?${params.toString()}`, false, true);
        const orderResponse = await fetch(`/api/xboot/steamKeyOrder/payOrder?${params.toString()}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!orderResponse.ok) {
            const errorData = await orderResponse.json();
            throw new Error(errorData.detail || '下单失败');
        }

        const orderResult = await orderResponse.json();

        if (orderResult.success) {
            // 获取支付链接和订单ID
            const alipayUrl = orderResult.result?.alipay || '';
            const payPrice = orderResult.result?.payPrice || '';
            const orderId = orderResult.result?.orderId || '';

            addCdkLog(`游戏 ${gameName} 下单成功，实际成交价: ¥${actualPrice}，订单价格: ¥${payPrice}`, false, true);
            
            // 检查是否需要自动支付监控和兑换（特殊模式 + AA支付 + 使用余额）
            if (orderSettings.isSpecialMode && payType === 'AA' && walletFlag === 'useBalance' && orderId) {
                addCdkLog(`⚡ 特殊模式下AA支付使用余额，启动自动支付监控`, false, true);

                // 性能优化：使用传入的sellerInfo中的游戏信息，避免查找
                if (sellerInfo && sellerInfo.gameId) {
                    localStorage.setItem(`processing_${sellerInfo.gameId}`, 'true');
                } else if (typeof monitoredGames !== 'undefined') {
                    const game = monitoredGames.find(g => g.name === gameName);
                    if (game) {
                        localStorage.setItem(`processing_${game.id}`, 'true');
                    }
                }

                // 使用实际成交价格作为支付金额验证
                startPaymentMonitoring(gameName, orderId, actualPrice);
            } else {
                addCdkLog(`支付链接已发送至邮箱，请查收`, false, true);
            }

            // 下单成功：检查是否需要停止监控
            const stopAfterOrder = localStorage.getItem('cdkStopAfterOrder') === 'true';
            if (stopAfterOrder) {
                addCdkLog(`游戏 ${gameName} 下单成功，根据设置停止监控`, false, true);

                // 性能优化：使用传入的sellerInfo中的游戏信息，避免查找
                let currentGame = null;
                if (sellerInfo && sellerInfo.gameId && typeof monitoredGames !== 'undefined') {
                    currentGame = monitoredGames.find(g => g.id === sellerInfo.gameId);
                } else if (typeof monitoredGames !== 'undefined') {
                    currentGame = monitoredGames.find(g => g.name === gameName);
                }

                // 根据是否有分组来决定停止策略
                if (currentGame && currentGame.game_group) {
                    addCdkLog(`🛑 停止监控整个分组 '${currentGame.game_group}'`, false, true);
                    setTimeout(() => stopMonitoringGameGroup(currentGame.game_group), 1000);
                } else {
                    addCdkLog(`🛑 停止监控游戏 ${gameName}`, false, true);
                    setTimeout(() => stopMonitoringGame(gameName), 1000);
                }
            } else {
                // 下单成功后继续监控，不再从监控列表中移除
                addCdkLog(`游戏 ${gameName} 下单成功，继续监控中...`, false, true);
            }
        } else {
            // 下单失败：不移除监控列表，继续监控
            addCdkLog(`游戏 ${gameName} 下单失败: ${orderResult.message || '未知错误'}，继续监控中...`, true);
        }
    } catch (error) {
        // 下单过程出错：不移除监控列表，继续监控
        addCdkLog(`游戏 ${gameName} 下单过程中出错: ${error.message}，继续监控中...`, true);
    }
}

// 处理价格监控的核心逻辑
async function handlePriceMonitoring(game, gameIndex, currentUpdateTime) {
    const maxAttempts = game.last_update_time ? 1 : 1; // 更新时获取1次，首次监控获取1次
    let hasOrdered = false; // 标记是否已下单
    
    for (let i = 1; i <= maxAttempts; i++) {
        if (!isCdkMonitoring) return; // 检查是否已停止监控
        
        try {
            // 等待间隔（第一次立即执行）
            if (i > 1) {
                if (!isCdkMonitoring) return; // 再次检查监控状态
                await new Promise(resolve => setTimeout(resolve, 8000));
            }
            
            if (maxAttempts > 1) {
                addCdkLog(`第 ${i}/${maxAttempts} 次获取游戏 ${game.name} 价格...`, false);
            }
            
            // 获取最新价格数据
            const priceData = await fetchGamePrice(game.id);
            if (!isCdkMonitoring) return; // 获取价格后检查监控状态
            
            if (!priceData) {
                addCdkLog(`第 ${i} 次获取: 游戏 ${game.name} 暂无卖家信息`, false);
                continue;
            }
            
            const { lowestPrice, sellersData } = priceData;
            addCdkLog(`第 ${i} 次获取: 游戏 ${game.name} 当前价格 ¥${lowestPrice}`, false, true);

            // 输出卖家信息用于调试
            if (sellersData && sellersData.length > 0) {
                const seller = sellersData[0];
                addCdkLog(`📊 卖家信息 - ID: ${seller.saleId}, 名称: ${seller.steamName || '未知'}, 交易数: ${seller.sold || 0}`, false, true);
            }
            
            // 使用现有的updateGameData函数更新数据
            await updateGameData(game.id, {
                current_price: lowestPrice,
                last_update_time: currentUpdateTime
            });
            
            if (!isCdkMonitoring) return; // 更新数据后检查监控状态
            
            // 在更新缓存前保存旧价格（避免引用修改问题）
            const oldPrice = monitoredGames[gameIndex].current_price;
            
            // 更新本地缓存
            monitoredGames[gameIndex].current_price = lowestPrice;
            monitoredGames[gameIndex].last_update_time = currentUpdateTime;
            
            // 记录价格变动（使用保存的旧价格值，并添加安全检查）
            if (oldPrice && oldPrice !== lowestPrice && typeof oldPrice === 'number' && typeof lowestPrice === 'number') {
                // 添加价格变动阈值检查，避免微小波动造成过多记录
                const priceChange = Math.abs(oldPrice - lowestPrice);
                const changePercentage = (priceChange / oldPrice) * 100;
                
                // 只记录变动超过0.01元或1%的价格变化
                if (priceChange >= 0.01 || changePercentage >= 1) {
                    addCdkLog(`📊 ${game.name} 价格变动: ¥${oldPrice} → ¥${lowestPrice} (变化${changePercentage.toFixed(2)}%)`, false, true);
                    recordPriceChange(game.name, game.id, oldPrice, lowestPrice);
                } else {
                    // 调试信息：微小变动不记录
                    console.debug(`${game.name} 价格微小变动被忽略: ¥${oldPrice} → ¥${lowestPrice} (变化${changePercentage.toFixed(4)}%)`);
                }
            }
            
            // 优先检查目标价格命中 - 最高优先级
            const targetHit = await checkAndHandleTargetPrice(game, gameIndex, lowestPrice, sellersData);
            
            if (targetHit) {
                hasOrdered = true;
                addCdkLog(`游戏 ${game.name} 已处理目标价格命中，停止后续获取`, false, true);
                break; // 命中目标价格后立即停止所有后续操作
            }
            
        } catch (error) {
            addCdkLog(`第 ${i} 次获取游戏 ${game.name} 价格失败: ${error.message}`, true);
        }
    }
    
    if (isCdkMonitoring) {
        // 完成后的统一处理
        const status = hasOrdered ? '已下单' : '监控完成';
        addCdkLog(`游戏 ${game.name} 价格获取${status}`, false, true);

        // DOM更新优化：只更新单个游戏的价格显示，而不是重新渲染整个列表
        updateSingleGamePrice(game.id, game.current_price);
    }
}

/**
 * DOM更新优化：只更新单个游戏的价格显示
 * @param {string} gameId - 游戏ID
 * @param {number} price - 新价格
 */
function updateSingleGamePrice(gameId, price) {
    try {
        // 查找对应游戏的价格显示元素
        const priceElement = document.querySelector(`[data-game-id="${gameId}"] .current-price`);
        if (priceElement && price !== undefined && price !== null) {
            priceElement.textContent = `¥${price}`;

            // 添加价格更新动画效果
            priceElement.classList.add('price-updated');
            setTimeout(() => {
                priceElement.classList.remove('price-updated');
            }, 1000);
        }

        // 更新最后更新时间显示
        const timeElement = document.querySelector(`[data-game-id="${gameId}"] .last-update-time`);
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString();
        }
    } catch (error) {
        // 如果单个更新失败，回退到全量渲染
        if (typeof renderMonitoredGames === 'function' && typeof monitoredGames !== 'undefined') {
            renderMonitoredGames(monitoredGames);
        }
    }
}

// 优化监控游戏信息函数 - 重构结构，确保下单优先级最高
async function monitorGameInfo(game) {
    try {
        if (!isCdkMonitoring) return; // 开始时检查监控状态
        
        // 获取游戏基本信息
        const response = await fetch(`/api/games/${game.id}/info`);
        if (!response.ok) {
            throw new Error(`获取游戏信息失败: ${response.status}`);
        }
        
        if (!isCdkMonitoring) return; // 获取响应后检查监控状态
        
        const gameInfo = await response.json();

        // 性能优化：快速提取updateTime
        const currentUpdateTime = gameInfo.updateTime;

        if (!currentUpdateTime) {
            addCdkLog(`未能获取到游戏 ${game.name} 的 updateTime 字段`, true);
            return;
        }
        
        // 找到游戏在数组中的索引
        const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
        if (gameIndex === -1) return;
        
        // 检查是否需要获取价格
        const needsPriceCheck = !game.last_update_time || currentUpdateTime !== game.last_update_time;
        
        
        if (needsPriceCheck) {
            if (!isCdkMonitoring) return; // 判断后再次检查监控状态
            
            if (game.last_update_time) {
                addCdkLog(`检测到游戏 ${game.name} 信息更新，开始连续获取价格...`, false, true);
            } else {
                addCdkLog(`游戏 ${game.name} 首次监控，获取初始价格...`, false);
            }
            
            // 连续获取价格，优先处理命中逻辑
            await handlePriceMonitoring(game, gameIndex, currentUpdateTime);
        }
        
    } catch (error) {
        addCdkLog(`监控游戏 ${game.name} 时发生错误: ${error.message}`, true);
        throw error;
    }
}

// 获取游戏价格数据
async function fetchGamePrice(gameId) {
    const sellersResponse = await fetch(`/api/games/${gameId}/sellers?pageSize=1`);
    if (!sellersResponse.ok) {
        throw new Error(`获取游戏卖家列表失败: ${sellersResponse.status}`);
    }
    
    const sellersData = await sellersResponse.json();
    if (!sellersData || sellersData.length === 0) {
        return null;
    }



    const lowestPrice = Math.min(...sellersData
        .filter(seller => seller.keyPrice && typeof seller.keyPrice === 'number')
        .map(seller => seller.keyPrice));

    return { lowestPrice, sellersData };
}

// 记录价格变动
function recordPriceChange(gameName, gameId, oldPrice, newPrice) {
    // 创建新的价格变动记录对象
    const timestamp = new Date().toLocaleString('zh-CN');
    const newChange = {
        gameId: gameId,
        gameName: gameName,
        oldPrice: parseFloat(oldPrice),
        newPrice: parseFloat(newPrice),
        timestamp: timestamp
    };
    
    // 获取现有的价格变动记录
    const storedChanges = localStorage.getItem('cdkPriceChanges');
    let priceChanges = [];
    
    if (storedChanges) {
        try {
            priceChanges = JSON.parse(storedChanges);
        } catch (e) {
            priceChanges = [];
        }
    }
    
    // 添加到价格变动记录的顶部
    priceChanges.unshift(newChange);
    
    // 最多保存100条记录到localStorage
    if (priceChanges.length > 100) {
        priceChanges = priceChanges.slice(0, 100);
    }
    
    // 保存到localStorage
    localStorage.setItem('cdkPriceChanges', JSON.stringify(priceChanges));
    
    // 在父窗口调用renderPriceChanges，但只显示前20条
    if (typeof renderPriceChanges === 'function') {
        const displayChanges = priceChanges.slice(0, 20); // 前端只显示最近20条
        renderPriceChanges(displayChanges);
    }
}



/**
 * 启动支付状态监控（特殊模式下AA支付使用余额时）
 */
async function startPaymentMonitoring(gameName, orderId, actualPrice = 0) {
    addCdkLog(`开始监控订单 ${orderId} 的支付状态（实际成交价: ¥${actualPrice}）...`, false, true);
    
    let checkCount = 0;
    const maxChecks = 8; // 增加到8次检查
    const checkInterval = 5000; // 5秒
    
    const checkPaymentStatus = async () => {
        checkCount++;
        
        try {
            addCdkLog(`第 ${checkCount} 次检查支付状态...`, false);
            
            const response = await fetch(`/api/xboot/steamKeyOrder/checkPay?orderId=${orderId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`支付状态检查失败: ${response.status}`);
            }
            
            const result = await response.json();
            
            // 调试日志：显示完整的支付状态响应
            console.debug(`支付状态检查响应:`, result);
            
            if (result.success && result.result) {
                const txStatus = result.result.txStatus;
                const pyWallet = parseFloat(result.result.pyWallet) || 0;
                
                if (txStatus === '03') {
                    // 检查支付金额是否匹配
                    if (actualPrice > 0 && Math.abs(pyWallet - actualPrice) < 0.01) {
                        // 支付成功且金额匹配
                        addCdkLog(`🎉 游戏 ${gameName} 支付成功！金额匹配: pyWallet=¥${pyWallet}, 实际成交价=¥${actualPrice}，开始自动兑换...`, false, true);
                        await performAutoRedeem(gameName, orderId);
                        return; // 结束监控
                    } else if (actualPrice > 0) {
                        // 支付成功但金额不匹配
                        addCdkLog(`⚠️ 游戏 ${gameName} 支付成功但金额不匹配！实际成交价: ¥${actualPrice}，pyWallet: ¥${pyWallet}，停止自动兑换`, true);
                        
                        // 清除处理状态，允许继续监控
                        if (typeof monitoredGames !== 'undefined') {
                            const game = monitoredGames.find(g => g.name === gameName);
                            if (game) {
                                localStorage.removeItem(`processing_${game.id}`);
                            }
                        }
                        return; // 结束监控但不兑换
                    } else {
                        // 没有实际价格时，按原逻辑处理
                        addCdkLog(`🎉 游戏 ${gameName} 支付成功！开始自动兑换...`, false, true);
                        await performAutoRedeem(gameName, orderId);
                        return; // 结束监控
                    }
                } else {
                    addCdkLog(`支付状态: ${txStatus}（金额: ¥${pyWallet}），继续等待...`, false);
                }
            } else {
                addCdkLog(`支付状态检查返回: ${result.message || '未知状态'}`, false);
            }
            
            // 如果还没有达到最大检查次数，继续检查
            if (checkCount < maxChecks) {
                setTimeout(checkPaymentStatus, checkInterval);
            } else {
                // 达到最大检查次数，认为支付失败
                addCdkLog(`❌ 游戏 ${gameName} 支付监控超时，重新回到监控状态`, true);
                addCdkLog(`订单 ${orderId} 将不再自动处理，请手动检查`, false);
                
                // 清除处理状态，允许继续监控
                if (typeof monitoredGames !== 'undefined') {
                    const game = monitoredGames.find(g => g.name === gameName);
                    if (game) {
                        localStorage.removeItem(`processing_${game.id}`);
                    }
                }
            }
            
        } catch (error) {
            addCdkLog(`支付状态检查出错: ${error.message}`, true);
            
            // 如果还没有达到最大检查次数，继续检查
            if (checkCount < maxChecks) {
                setTimeout(checkPaymentStatus, checkInterval);
            } else {
                addCdkLog(`❌ 游戏 ${gameName} 支付监控失败，重新回到监控状态`, true);
                
                // 清除处理状态，允许继续监控
                if (typeof monitoredGames !== 'undefined') {
                    const game = monitoredGames.find(g => g.name === gameName);
                    if (game) {
                        localStorage.removeItem(`processing_${game.id}`);
                    }
                }
            }
        }
    };
    
    // 开始第一次检查
    setTimeout(checkPaymentStatus, checkInterval);
}

/**
 * 执行自动兑换
 */
async function performAutoRedeem(gameName, orderId) {
    try {
        addCdkLog(`正在为游戏 ${gameName} 执行自动兑换...`, false, true);
        
        // 查找当前游戏的分组信息（可能已从监控列表移除）
        let currentGame = null;
        if (typeof monitoredGames !== 'undefined') {
            currentGame = monitoredGames.find(g => g.name === gameName);
            if (currentGame) {
                addCdkLog(`🛑 开始兑换流程，停止监控游戏 ${gameName}`, false, true);
                // 一旦开始兑换流程，根据是否有分组来决定停止策略
                if (currentGame.game_group) {
                    addCdkLog(`🛑 停止监控整个分组 '${currentGame.game_group}'`, false, true);
                    await stopMonitoringGameGroup(currentGame.game_group);
                } else {
                    await stopMonitoringGame(gameName);
                }
            } else {
                addCdkLog(`⚠️ 未找到游戏 ${gameName} 在监控列表中`, false);
            }
        } else {
            addCdkLog(`🛑 开始兑换流程，停止监控游戏 ${gameName}`, false, true);
        }
        
        const redeemData = new URLSearchParams({
            orderId: orderId,
            player: 'HEIXS21',  // 固定值
            password: '',
            saveLog: 'false'
        });
        
        const response = await fetch('/api/xboot/steamKeyOrder/keyFirstV2', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: redeemData.toString()
        });
        
        if (!response.ok) {
            throw new Error(`兑换请求失败: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            addCdkLog(`🎁 游戏 ${gameName} 自动兑换成功！`, false, true);
            addCdkLog(`兑换结果: ${result.message || '激活码已发送'}`, false, true);
        } else {
            addCdkLog(`❌ 游戏 ${gameName} 兑换失败: ${result.message || '未知错误'}`, true);
        }
        
    } catch (error) {
        addCdkLog(`游戏 ${gameName} 自动兑换过程中出错: ${error.message}`, true);
    }
}

/**
 * 停止监控指定游戏（根据游戏名称）
 */
async function stopMonitoringGame(gameName) {
    try {
        // 在全局变量monitoredGames中查找对应的游戏
        if (typeof monitoredGames !== 'undefined') {
            const game = monitoredGames.find(g => g.name === gameName);
            if (game) {
                // 清除处理状态
                localStorage.removeItem(`processing_${game.id}`);
                
                // 调用全局的removeMonitoredGame函数来移除游戏
                if (typeof removeMonitoredGame === 'function') {
                    removeMonitoredGame(game.id, true); // 跳过确认框
                    addCdkLog(`🛑 游戏 ${gameName} 已从监控列表中移除`, false, true);
                } else {
                    addCdkLog(`⚠️ 无法移除游戏 ${gameName}：removeMonitoredGame函数不可用`, true);
                }
            } else {
                addCdkLog(`⚠️ 未找到游戏 ${gameName} 在监控列表中`, false);
            }
        } else {
            addCdkLog(`⚠️ monitoredGames变量不可用，无法移除游戏 ${gameName}`, true);
        }
    } catch (error) {
        addCdkLog(`停止监控游戏 ${gameName} 时出错: ${error.message}`, true);
    }
}

/**
 * 停止监控整个游戏分组
 */
async function stopMonitoringGameGroup(gameGroup) {
    try {
        if (!gameGroup) {
            addCdkLog(`⚠️ 游戏分组为空，无法停止分组监控`, false);
            return;
        }

        addCdkLog(`🛑 检测到分组 '${gameGroup}' 中有游戏下单成功，停止监控整个分组...`, false, true);

        // 调用后端API移除整个分组
        const response = await fetch(`/api/games/groups/${encodeURIComponent(gameGroup)}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            const result = await response.json();
            addCdkLog(`✅ ${result.message}`, false, true);
            
            // 清除所有相关的处理状态
            if (typeof monitoredGames !== 'undefined') {
                monitoredGames.forEach(game => {
                    if (game.game_group === gameGroup) {
                        localStorage.removeItem(`processing_${game.id}`);
                        localStorage.removeItem(`ordered_${game.id}`);
                    }
                });
            }

            // 重新加载监控游戏列表
            if (typeof window.loadMonitoredGames === 'function') {
                await window.loadMonitoredGames();
            } else if (typeof loadMonitoredGames === 'function') {
                await loadMonitoredGames();
            } else {
                // 手动刷新页面作为后备方案
                window.location.reload();
            }
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        addCdkLog(`停止监控游戏分组 '${gameGroup}' 时出错: ${error.message}`, true);
    }
}

// ==================== 批量监控优化功能 ====================

/**
 * 并行获取多个游戏的信息
 * @param {Array} games - 游戏列表
 * @returns {Map} 游戏ID到信息结果的映射
 */
async function fetchMultipleGameInfos(games) {
    if (!games || games.length === 0) return new Map();

    const batchSize = 16;
    const batches = [];

    // 将游戏分成多个批次
    for (let i = 0; i < games.length; i += batchSize) {
        batches.push(games.slice(i, i + batchSize));
    }

    // 暂时回退到原来的方式，直到批量API稳定
    const results = new Map();

    for (let i = 0; i < batches.length; i++) {
        if (!isCdkMonitoring) break;

        const batch = batches[i];
        const batchResult = await fetchSingleBatchSerial(batch);

        // 合并结果
        batchResult.forEach((value, key) => {
            results.set(key, value);
        });

        // 批次间延迟
        if (i < batches.length - 1 && isCdkMonitoring) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    return results;
}

/**
 * 串行处理单个批次（原来的方式）
 */
async function fetchSingleBatchSerial(batch) {
    const results = new Map();

    const batchPromises = batch.map(async (game) => {
        try {
            const response = await fetch(`/api/games/${game.id}/info`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const gameInfo = await response.json();

            // 性能优化：快速提取updateTime，后端已优化
            const updateTime = gameInfo.updateTime;
            if (!updateTime) {
                console.warn(`游戏 ${game.name} (${game.id}) 未获取到updateTime`);
            }

            return {
                gameId: game.id,
                gameName: game.name,
                success: true,
                updateTime: updateTime,
                data: gameInfo
            };
        } catch (error) {
            addCdkLog(`❌ 获取游戏 ${game.name} 信息失败: ${error.message}`, true);
            return {
                gameId: game.id,
                gameName: game.name,
                success: false,
                error: error.message
            };
        }
    });

    const batchResults = await Promise.all(batchPromises);
    batchResults.forEach(result => {
        results.set(result.gameId, result);
    });

    return results;
}

/**
 * 真正并行处理所有批次（使用后端批量API）
 */
async function fetchAllBatchesParallel(batches) {
    const results = new Map();

    // 使用后端批量API，真正的多线程处理
    const batchProcessors = batches.map(async (batch, batchIndex) => {
        // 错开启动时间，避免同时冲击API
        const initialDelay = batchIndex * 30; // 减少到30ms
        await new Promise(resolve => setTimeout(resolve, initialDelay));

        try {
            // 使用批量API接口
            const gameIds = batch.map(game => game.id);
            const response = await fetch('/api/games/batch/info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ game_ids: gameIds })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const batchData = await response.json();
            const batchResults = new Map();

            // 处理批量结果
            batch.forEach(game => {
                const gameResult = batchData.results[game.id];
                if (gameResult && gameResult.success) {
                    batchResults.set(game.id, {
                        gameId: game.id,
                        gameName: game.name,
                        success: true,
                        updateTime: gameResult.updateTime,
                        data: gameResult
                    });
                } else {
                    batchResults.set(game.id, {
                        gameId: game.id,
                        gameName: game.name,
                        success: false,
                        error: gameResult?.error || 'Unknown error'
                    });
                }
            });

            return batchResults;

        } catch (error) {
            // 批量API失败时回退到单个请求
            console.warn(`批量API失败，回退到单个请求: ${error.message}`);
            return await fetchSingleBatchSerial(batch);
        }
    });

    // 所有批次真正并行执行
    const allBatchResults = await Promise.all(batchProcessors);

    // 合并所有批次的结果
    allBatchResults.forEach(batchResult => {
        batchResult.forEach((value, key) => {
            results.set(key, value);
        });
    });

    return results;
}

/**
 * 批量处理游戏信息监控
 * @param {Array} games - 游戏列表
 */
async function batchMonitorGameInfos(games) {
    if (!games || games.length === 0) return;
    if (!isCdkMonitoring) return;

    try {
        // 减少冗余日志，只在有更新时输出

        // 1. 并行获取所有游戏的信息
        const gameInfoResults = await fetchMultipleGameInfos(games);
        if (!isCdkMonitoring) return;

        // 2. 快速检查哪些游戏需要获取价格
        const needsPriceCheckGames = [];

        for (const game of games) {
            const infoResult = gameInfoResults.get(game.id);

            // 快速跳过失败的游戏
            if (!infoResult?.success) {
                addCdkLog(`⚠️ ${game.name} 信息获取失败，跳过`, true);
                continue;
            }

            const currentUpdateTime = infoResult.updateTime;
            if (!currentUpdateTime) {
                addCdkLog(`⚠️ ${game.name} 未获取到 updateTime，跳过`, true);
                continue;
            }

            // 性能优化：快速updateTime比较
            if (!game.last_update_time || currentUpdateTime !== game.last_update_time) {
                needsPriceCheckGames.push({
                    ...game,
                    currentUpdateTime: currentUpdateTime
                });
            }
            // 移除无需更新的游戏统计，减少处理开销
        }

        // 3. 批量获取价格（如果需要）
        if (needsPriceCheckGames.length > 0) {
            addCdkLog(`💰 ${needsPriceCheckGames.length} 个游戏需要价格检查`, false, true);
            await handleBatchPriceMonitoring(needsPriceCheckGames);
        }
        // 移除"无需更新"相关日志

    } catch (error) {
        addCdkLog(`❌ 批量监控过程中发生错误: ${error.message}`, true);
        console.error('批量监控错误:', error);
    }
}

/**
 * 批量并行处理价格监控 - 性能优化版本
 * @param {Array} games - 需要价格检查的游戏列表（包含currentUpdateTime）
 */
async function handleBatchPriceMonitoring(games) {
    if (!games || games.length === 0) return;
    if (!isCdkMonitoring) return;

    // 性能优化：并行处理价格获取，而不是串行
    const priceCheckPromises = games.map(async (game) => {
        if (!isCdkMonitoring) return;

        try {
            const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
            if (gameIndex === -1) return;

            // 简化日志，减少输出
            if (!game.last_update_time) {
                addCdkLog(`🔍 ${game.name} 首次获取价格`, false);
            }

            // 使用现有的价格监控逻辑，但并行执行
            await handlePriceMonitoring(game, gameIndex, game.currentUpdateTime);

        } catch (error) {
            addCdkLog(`❌ ${game.name} 价格监控失败: ${error.message}`, true);
        }
    });

    // 并行执行所有价格检查，但限制并发数量避免过载
    const concurrencyLimit = 8; // 最多8个游戏同时获取价格
    for (let i = 0; i < priceCheckPromises.length; i += concurrencyLimit) {
        if (!isCdkMonitoring) break;

        const batch = priceCheckPromises.slice(i, i + concurrencyLimit);
        await Promise.all(batch);

        // 批次间短暂延迟，避免API过载
        if (i + concurrencyLimit < priceCheckPromises.length && isCdkMonitoring) {
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }
}

// 导出核心功能
window.cdkMonitorCore = {
    monitorGameInfo,
    handlePriceMonitoring,
    checkAndHandleTargetPrice,
    attemptAutoOrder,
    placeCdkOrder,
    fetchGamePrice,
    getLocalOrderSettings,
    isSpecialModeEnabledLocal,
    stopMonitoringGame,
    stopMonitoringGameGroup,
    startPaymentMonitoring,
    performAutoRedeem,
    // 新增批量监控功能
    fetchMultipleGameInfos,
    batchMonitorGameInfos,
    // 下单配置预热功能
    preloadOrderSettings,
    getFastOrderSettings
};